using System;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using QFramework;
using TFG.UI.Common;
using UnityEngine;

namespace TFG.UI
{
    //True是Email,False是phoneNumber
    [Serializable]
    public partial class UIAutoLoginToggleComponent : UIComponent, ICanSendEvent, ICanRegisterEvent
    {
        public CanvasGroup CanvasGroup;
        public UIToggleBox AutoToggle;

        private LocalizationSystem LocalizationSystem;
        /// <summary>
        /// 初始化，外部设置默认值、并应用表现
        /// </summary>
        public void InitAndShow()
        {
            switch (GetArchitecture().GetModel<ClientAccountPreferencesModel>().IsAutoLogin)
            {
                case false:
                    AutoToggle.Initialize(false,false);
                    break;
                case true:
                    AutoToggle.Initialize(true,false);
                    break;
            }

            LocalizationSystem = GetArchitecture().GetSystem<LocalizationSystem>();
            AutoToggle.OnValueChanged += ChangeAutoLogin;
            CanvasGroup.EnableView();
        }

        /// <summary>
        /// 初始化，外部重置表现、隐藏输入框
        /// </summary>
        public void HideAndDeinit()
        {
            Clear();
            CanvasGroup.DisableView();
        }

        private void Clear()
        {
            AutoToggle.ClearAllEventListeners();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Clear();
        }

        private void ChangeAutoLogin(bool isAutoLogin)
        {
            GetArchitecture().GetModel<ClientAccountPreferencesModel>().IsAutoLogin = isAutoLogin;
            AutoLoginChange().Forget();
        }

        private async UniTask AutoLoginChange()
        {
            //var msg = LocalizationSystem.GetLocalizationContent("DialogTestSDK");
            var ok = LocalizationSystem.GetLocalizationContent("OK");
            await UIDialogHelper.ShowNormalStyle("", "测试自动登录占位",true,false,confirmText:ok);
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
