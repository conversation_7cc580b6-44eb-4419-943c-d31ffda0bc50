using System;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient.Command;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    [Serializable]
    public class StateResetVerificationCodeWithEmail : UIStateBase
    {
        private UILoginPanel _uiLoginPanel;
        public void BindPanel(UILoginPanel parent) => _uiLoginPanel = parent;

        public override UIStateBase ParentState
        {
            get => _uiLoginPanel.StateResetLayerState;
            set { }
        }

        public override async UniTask Enter(object data = null)
        {
            _uiLoginPanel.UIBackToLastStateButtonComponent.InitAndShow(_uiLoginPanel.UIfsmManager,
                _uiLoginPanel.StateLanding);
            await _uiLoginPanel.UIEmailInputComponent.InitAndShow(_uiLoginPanel.TmpEmail);
            _uiLoginPanel.UIEmailInputComponent.AfterChecked += UIEmailInputComponentOnAfterChecked;
            _uiLoginPanel.UISendVerificationRequestComponent.InitAndShow("EmailResetVerification");
            await _uiLoginPanel.UIVerificationCodeInputComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.InitAndShow();
            _uiLoginPanel.UINextComponent.NextButton.OnClickEvent += NextButtonOnOnClickEvent;
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent += SendVerificationRequestBtnOnOnClickEvent;
            this.RegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
        }
        public override UniTask Exit()
        {
            _uiLoginPanel.TmpEmail = _uiLoginPanel.UIEmailInputComponent.Email;
            _uiLoginPanel.UINextComponent.HideAndDeinit();
            _uiLoginPanel.UIVerificationCodeInputComponent.HideAndDeinit();
            _uiLoginPanel.UISendVerificationRequestComponent.HideAndDeinit();
            _uiLoginPanel.UIEmailInputComponent.HideAndDeinit();
            _uiLoginPanel.UIBackToLastStateButtonComponent.HideAndDeinit();
            _uiLoginPanel.UINextComponent.NextButton.ClearAllEvents();
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.OnClickEvent -= SendVerificationRequestBtnOnOnClickEvent;
            this.UnRegisterEvent<ViewAccountTypeChangedEvent>(OnViewAccountTypeChanged);
            return UniTask.CompletedTask;
        }

        private void UIEmailInputComponentOnAfterChecked(EmailValidationResult obj)
        {
            if (obj is not EmailValidationResult.Valid)
            {
                _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.DisableBtn();
                return;
            }
            _uiLoginPanel.UISendVerificationRequestComponent.SendVerificationRequestBtn.EnableBtn();
        }

        private void SendVerificationRequestBtnOnOnClickEvent()
        {
            SendVerificationCode().Forget();
        }

        private async UniTask SendVerificationCode()
        {
            if (!await _uiLoginPanel.ValidateEmailAsync(_uiLoginPanel.UINextComponent.NextButton))
            {
                _uiLoginPanel.UISendVerificationRequestComponent.ForceResetCooldown();
                return;
            }

            this.SendCommand(new GetVerificationCodeResetPasswordCommand(
                _uiLoginPanel.UIEmailInputComponent.Email, _uiLoginPanel.TmpEmail, "", 0));
        }

        private void NextButtonOnOnClickEvent()
        {
            RegisterVerificationCodeWithEmailCheckInput().Forget();
        }



        private void OnViewAccountTypeChanged(ViewAccountTypeChangedEvent e)
        {
            if (e.AccountType == AccountType.Email)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithEmail)
                    .Forget();
            }
            else if (e.AccountType == AccountType.PhoneNumber)
            {
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetVerificationCodeWithPhone)
                    .Forget();
            }

            _uiLoginPanel.PreferencesModel.LastPhone = "";
            _uiLoginPanel.PreferencesModel.LastEmail = "";
            _uiLoginPanel.PreferencesModel.LastCountryCodeIndex = 0;
        }

        private async UniTask RegisterVerificationCodeWithEmailCheckInput()
        {
            _uiLoginPanel.UINextComponent.NextButton.DisableBtn();
            try
            {
                if (!await _uiLoginPanel.ValidateEmailAsync(_uiLoginPanel.UINextComponent.NextButton)) return;
                if (!await _uiLoginPanel.ValidateVerificationCodeAsync(_uiLoginPanel.UINextComponent.NextButton)) return;
                _uiLoginPanel.TmpEmail = _uiLoginPanel.UIEmailInputComponent.Email;
                _uiLoginPanel.TmpVerificationCode =
                    _uiLoginPanel.UIVerificationCodeInputComponent.VerificationCode;
                _uiLoginPanel.UIfsmManager.ChangeState(_uiLoginPanel.StateResetEmailSettingPassword)
                    .Forget();
            }
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}::CheckInput]+{e.Message} ");
            }
        }

        public override void OnUpdate()
        {
        }
    }
} 
